import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/components/StartupFunding.css';
import DashboardLayout from './DashboardLayout';

// Import icons
import overviewIcon from '../assets/overview.png';
import walletMoneyIcon from '../assets/wallet-money.png';
import arrow2Icon from '../assets/arrow-2.png';
import cardCoinIcon from '../assets/card-coin.png';
import moneysIcon from '../assets/moneys.png';
import percentageSquareIcon from '../assets/percentage-square.png';
import chartIcon from '../assets/chart.png';
import spanImage from '../assets/span.png';
import informationCircleIcon from '../assets/information-circle.png';
import trendUpIcon from '../assets/trend-up.png';

interface StartupFundingProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const StartupFunding: React.FC<StartupFundingProps> = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('startup-funding');
  const sidebarNavRef = useRef<HTMLElement>(null);

  // Center active tab on mobile
  useEffect(() => {
    const centerActiveTab = () => {
      if (window.innerWidth <= 768 && sidebarNavRef.current) {
        const activeButton = sidebarNavRef.current.querySelector('.sidebar-item.active') as HTMLElement;
        if (activeButton) {
          const container = sidebarNavRef.current;
          const containerWidth = container.offsetWidth;
          const buttonWidth = activeButton.offsetWidth;
          const buttonLeft = activeButton.offsetLeft;

          const scrollLeft = buttonLeft - (containerWidth / 2) + (buttonWidth / 2);
          container.scrollTo({
            left: scrollLeft,
            behavior: 'smooth'
          });
        }
      }
    };

    centerActiveTab();
    window.addEventListener('resize', centerActiveTab);
    return () => window.removeEventListener('resize', centerActiveTab);
  }, [activeTab]);
  const [searchQuery, setSearchQuery] = useState('');
  const [regionFilter, setRegionFilter] = useState('all-regions');
  const [sortFilter, setSortFilter] = useState('most-recent');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const sidebarItems = [
    { id: 'overview', label: 'Overview', icon: overviewIcon },
    { id: 'live-reserve', label: 'Live Reserve', icon: walletMoneyIcon },
    { id: 'daily-transactions', label: 'Daily Transactions', icon: arrow2Icon },
    { id: 'real-time-staking', label: 'Real-Time Staking', icon: cardCoinIcon },
    { id: 'startup-funding', label: 'Startup Funding', icon: moneysIcon, active: true },
    { id: 'roi-distribution', label: 'ROI Distribution', icon: percentageSquareIcon },
    { id: 'visual-analytics', label: 'Visual Analytics', icon: chartIcon }
  ];

  const regionFilters = [
    { id: 'all-regions', label: 'All regions' },
    { id: 'usa', label: 'USA' },
    { id: 'kenya', label: 'Kenya' },
    { id: 'nigeria', label: 'Nigeria' }
  ];

  const sortFilters = [
    { id: 'most-recent', label: 'From most recent' },
    { id: 'oldest', label: 'From oldest' },
    { id: 'highest-amount', label: 'Highest amount' },
    { id: 'lowest-amount', label: 'Lowest amount' }
  ];

  // Mock startup funding data
  const startupData = Array.from({ length: 50 }, (_, index) => {
    const startupNames = ['Grandex Agrotech', 'TechFlow Solutions', 'GreenEnergy Corp', 'DataMind AI', 'HealthTech Plus'];
    const stages = ['MVP', 'Launched', 'Seed', 'Series A'];
    const amounts = [5000, 8000, 10000, 15000, 20000];
    const regions = ['Kenya', 'USA', 'Nigeria', 'Ghana'];
    
    return {
      id: index + 1,
      name: startupNames[index % startupNames.length],
      stage: stages[index % stages.length],
      amount: amounts[index % amounts.length],
      date: 'Jan, 5, 2025',
      region: regions[index % regions.length]
    };
  });

  const totalStartups = '11,302';
  const totalAmount = '91,000';
  const availableFunds = '$43,700';
  const changePercent = '+4.8% Today';

  const handleSidebarClick = (itemId: string) => {
    setActiveTab(itemId);

    switch (itemId) {
      case 'overview':
        navigate('/overview');
        break;
      case 'live-reserve':
        navigate('/live-reserve');
        break;
      case 'daily-transactions':
        navigate('/daily-transactions');
        break;
      case 'real-time-staking':
        navigate('/real-time-staking');
        break;
      case 'startup-funding':
        // Already on this page
        break;
      case 'roi-distribution':
        navigate('/roi-distribution');
        break;
      case 'visual-analytics':
        navigate('/visual-analytics');
        break;
      default:
        console.log(`Loading ${itemId} page...`);
    }
  };

  const totalPages = Math.ceil(startupData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentStartupData = startupData.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const renderPagination = () => {
    const centerPages = [];
    const maxVisiblePages = 5;

    // Page numbers for center
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      centerPages.push(
        <button
          key={i}
          className={`pagination-btn ${currentPage === i ? 'active' : ''}`}
          onClick={() => handlePageChange(i)}
        >
          {i}
        </button>
      );
    }

    // Add ellipsis if needed
    if (startPage > 1) {
      centerPages.unshift(
        <span key="start-ellipsis" className="pagination-ellipsis">...</span>
      );
    }
    if (endPage < totalPages) {
      centerPages.push(
        <span key="end-ellipsis" className="pagination-ellipsis">...</span>
      );
    }

    return (
      <>
        {/* Previous button */}
        <button
          className={`pagination-btn ${currentPage === 1 ? 'disabled' : ''}`}
          onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          ← Previous
        </button>

        {/* Center page numbers */}
        <div className="pagination-center">
          {centerPages}
        </div>

        {/* Next button */}
        <button
          className={`pagination-btn ${currentPage === totalPages ? 'disabled' : ''}`}
          onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          Next →
        </button>
      </>
    );
  };

  return (
    <DashboardLayout className="startup-funding-container">
      <div className="startup-funding-content">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">
            <div className="title-with-span">
              Treasury
              <img src={spanImage} alt="Decorative span" className="title-span" />
            </div>
            Dashboard
          </h1>
          <p className="page-subtitle">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            Empowering you to stake with trust.
          </p>
        </div>

        <div className="dashboard-layout">
          {/* Sidebar */}
          <aside className="dashboard-sidebar">
            <nav className="sidebar-nav" ref={sidebarNavRef}>
              {sidebarItems.map((item) => (
                <button
                  key={item.id}
                  className={`sidebar-item ${activeTab === item.id ? 'active' : ''}`}
                  onClick={() => handleSidebarClick(item.id)}
                >
                  <img src={item.icon} alt={item.label} className="sidebar-icon" />
                  <span className="sidebar-label">{item.label}</span>
                </button>
              ))}
            </nav>
          </aside>

          {/* Main Dashboard Content */}
          <div className="dashboard-content">
            <div className="funding-header">
              <h2 className="section-title">Startup Funding</h2>
            </div>

            {/* Stats Cards */}
            <div className="funding-stats">
              <div className="stats-card">
                <div className="stats-label">TOTAL STARTUPS FUNDED</div>
                <div className="stats-value">{totalStartups}</div>
                <div className="stats-sublabel">TOTAL FUNDED AMOUNT</div>
                <div className="stats-subvalue">{totalAmount}</div>
              </div>

              <div className="stats-card funds-card">
                <div className="stats-label">
                  FUNDS AVAILABLE TO FUND STARTUPS
                  <div className="tooltip-container">
                    <img src={informationCircleIcon} alt="Info" className="info-icon" />
                    <div className="tooltip">
                      We dedicate 5% of our total profits to backing startups, including ideas pitched by stakers like you.
                    </div>
                  </div>
                </div>
                <div className="stats-value">{availableFunds}</div>
                <div className="stats-change">
                  <img src={trendUpIcon} alt="Trend up" className="trend-icon" />
                  {changePercent}
                </div>
              </div>
            </div>

            {/* Funding History */}
            <div className="funding-history">
              {/* Filters */}
              <div className="funding-filters">
                <div className="search-filter">
                  <input
                    type="text"
                    placeholder="Search startup name"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="search-input"
                  />
                </div>
                
                <div className="dropdown-filters">
                  <select
                    value={regionFilter}
                    onChange={(e) => setRegionFilter(e.target.value)}
                    className="filter-select"
                  >
                    {regionFilters.map((filter) => (
                      <option key={filter.id} value={filter.id}>
                        {filter.label}
                      </option>
                    ))}
                  </select>

                  <select
                    value={sortFilter}
                    onChange={(e) => setSortFilter(e.target.value)}
                    className="filter-select"
                  >
                    {sortFilters.map((filter) => (
                      <option key={filter.id} value={filter.id}>
                        {filter.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Table */}
              <div className="funding-table">
                <div className="table-header">
                  <div className="table-cell">Startup Name</div>
                  <div className="table-cell">Stage</div>
                  <div className="table-cell">Funding Amount</div>
                  <div className="table-cell">Funding Date</div>
                  <div className="table-cell">Region</div>
                </div>

                {currentStartupData.map((startup, index) => (
                  <div key={startup.id} className="table-row">
                    <div className="table-cell">{startIndex + index + 1}. {startup.name}</div>
                    <div className="table-cell" data-label="Stage">
                      <span className={`stage-badge ${startup.stage.toLowerCase().replace(' ', '-')}`}>
                        {startup.stage}
                      </span>
                    </div>
                    <div className="table-cell" data-label="Funding Amount">${startup.amount.toLocaleString()}</div>
                    <div className="table-cell" data-label="Funding Date">{startup.date}</div>
                    <div className="table-cell" data-label="Region">{startup.region}</div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              <div className="pagination">
                {renderPagination()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default StartupFunding;
