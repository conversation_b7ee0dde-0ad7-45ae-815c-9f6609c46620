/* Visual Analytics Container */
.visual-analytics-container {
  min-height: 100vh;
}

.visual-analytics-content {
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
}

/* Page Header */
.page-header {
  padding: 60px 40px 40px;
  text-align: center;
  position: relative;
}

.page-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 48px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.title-with-span {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.title-span {
  width: 200px;
  height: auto;
  opacity: 0.8;
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  gap: 32px;
  padding: 0 40px;
  min-height: calc(100vh - 200px);
}

/* Sidebar */
.dashboard-sidebar {
  width: 280px;
  background: rgba(38, 38, 38, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 32px 24px;
  height: fit-content;
  position: sticky;
  top: 120px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background: transparent;
  border: none;
  border-radius: 12px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
}

.sidebar-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
}

.sidebar-item.active {
  background: rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar-icon {
  width: 20px;
  height: 20px;
  opacity: 0.8;
}

.sidebar-item.active .sidebar-icon {
  opacity: 1;
}

.sidebar-label {
  flex: 1;
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  min-width: 0;
}

.visual-analytics-header {
  margin-bottom: 32px;
}

.section-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

/* Charts Container */
.charts-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 0 40px 40px;
}

/* Chart Section */
.chart-section {
  background: rgba(38, 38, 38, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.chart-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

.chart-filter {
  display: flex;
  align-items: center;
}

.filter-select {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 12px 40px 12px 16px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  appearance: none;
  min-width: 140px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23FFFFFF' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
}

.filter-select:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.3);
}

.filter-select option {
  background: #262626;
  color: #FFFFFF;
  padding: 8px;
}

/* Chart Container */
.chart-container {
  height: 400px;
  width: 100%;
}

.bar-chart-container {
  height: 450px;
}

/* Pie Chart Specific Styles */
.pie-chart-section {
  /* Specific styles for pie chart section if needed */
}

.pie-chart-container {
  display: flex;
  gap: 40px;
  align-items: center;
}

.pie-chart-wrapper {
  flex: 1;
  position: relative;
  height: 300px;
}

.pie-chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.total-value-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #999999;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.total-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
}

.pie-chart-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 300px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-details {
  flex: 1;
}

.legend-name {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: 2px;
}

.legend-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 2px;
}

.legend-percentage {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #999999;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .page-header {
    padding: 40px 20px 30px;
  }

  .page-title {
    font-size: 42px;
  }

  .title-span {
    max-width: 180px;
  }

  .dashboard-layout {
    flex-direction: column;
    padding: 0 20px;
    gap: 24px;
  }

  .dashboard-sidebar {
    width: 100%;
    position: static;
    order: 1;
    top: auto;
    padding: 12px 16px;
    margin-bottom: 24px;
    border-radius: 12px;
    height: 80px;
    display: flex;
    align-items: center;
  }

  .sidebar-nav {
    flex-direction: row;
    overflow-x: auto;
    gap: 16px;
    padding: 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 100%;
  }

  .sidebar-nav::-webkit-scrollbar {
    display: none;
  }

  .sidebar-item {
    white-space: nowrap;
    min-width: 120px;
    max-width: 160px;
    padding: 0 14px;
    flex-shrink: 0;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    line-height: 1.2;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-sizing: border-box;
  }

  .sidebar-icon {
    width: 16px;
    height: 16px;
  }

  .dashboard-content {
    order: 2;
  }

  .visual-analytics-header {
    margin-bottom: 24px;
  }

  .section-title {
    font-size: 24px;
  }

  .charts-container {
    gap: 24px;
  }

  .chart-section {
    padding: 20px;
    border-radius: 16px;
  }

  .chart-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
    margin-bottom: 20px;
  }

  .chart-title {
    font-size: 18px;
  }

  .chart-filter {
    width: 100%;
  }

  .filter-select {
    width: 100%;
    min-width: auto;
    padding: 12px 40px 12px 16px;
    font-size: 14px;
  }

  .chart-container {
    height: 280px;
    margin: 0 -10px;
    padding: 0 10px;
  }

  .bar-chart-container {
    height: 320px;
  }

  /* Pie Chart Mobile */
  .pie-chart-container {
    flex-direction: column;
    gap: 24px;
  }

  .pie-chart-wrapper {
    height: 250px;
  }

  .total-value {
    font-size: 20px;
  }

  .pie-chart-legend {
    max-width: 100%;
    gap: 16px;
  }

  .legend-item {
    gap: 10px;
  }

  .legend-name {
    font-size: 13px;
  }

  .legend-value {
    font-size: 14px;
  }

  .legend-percentage {
    font-size: 11px;
  }

  /* Ensure chart text is readable on mobile */
  .chart-container .recharts-text {
    font-size: 11px !important;
    font-family: 'Montserrat', sans-serif !important;
  }

  .chart-container .recharts-cartesian-axis-tick-value {
    font-size: 10px !important;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .title-text {
    font-size: 36px;
  }

  .charts-container {
    padding: 0 16px 40px;
  }

  .chart-section {
    padding: 16px;
  }

  .chart-container {
    height: 260px;
    margin: 0 -8px;
    padding: 0 8px;
  }

  .bar-chart-container {
    height: 300px;
  }

  .chart-title {
    font-size: 16px;
  }

  .pie-chart-wrapper {
    height: 220px;
  }

  .total-value {
    font-size: 18px;
  }
}
