import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/components/ROIDistribution.css';
import DashboardLayout from './DashboardLayout';

// Import icons
import overviewIcon from '../assets/overview.png';
import walletMoneyIcon from '../assets/wallet-money.png';
import arrow2Icon from '../assets/arrow-2.png';
import cardCoinIcon from '../assets/card-coin.png';
import moneysIcon from '../assets/moneys.png';
import percentageSquareIcon from '../assets/percentage-square.png';
import chartIcon from '../assets/chart.png';
import spanImage from '../assets/span.png';
import trendUpIcon from '../assets/trend-up.png';

interface ROIDistributionProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const ROIDistribution: React.FC<ROIDistributionProps> = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('roi-distribution');
  const [timeFilter, setTimeFilter] = useState('this-year');

  const sidebarItems = [
    { id: 'overview', label: 'Overview', icon: overviewIcon },
    { id: 'live-reserve', label: 'Live Reserve', icon: walletMoneyIcon },
    { id: 'daily-transactions', label: 'Daily Transactions', icon: arrow2Icon },
    { id: 'real-time-staking', label: 'Real-Time Staking', icon: cardCoinIcon },
    { id: 'startup-funding', label: 'Startup Funding', icon: moneysIcon },
    { id: 'roi-distribution', label: 'ROI Distribution', icon: percentageSquareIcon, active: true },
    { id: 'visual-analytics', label: 'Visual Analytics', icon: chartIcon }
  ];

  const timeFilters = [
    { id: 'this-year', label: 'This year' },
    { id: 'last-year', label: 'Last year' },
    { id: 'all-time', label: 'All time' }
  ];

  const totalPaidOut = '$243,700';
  const averageDailyPayout = '$24,500';
  const changePercent = '+0.8% Today';

  // Mock chart data for monthly ROI payouts
  const chartData = [
    { month: 'Jan', value: 750000 },
    { month: 'Feb', value: 800000 },
    { month: 'Mar', value: 850000 },
    { month: 'Apr', value: 900000 },
    { month: 'May', value: 950000 },
    { month: 'Jun', value: 1000000 },
    { month: 'Jul', value: 1050000 },
    { month: 'Aug', value: 1100000 }
  ];

  const handleSidebarClick = (itemId: string) => {
    setActiveTab(itemId);

    switch (itemId) {
      case 'overview':
        navigate('/overview');
        break;
      case 'live-reserve':
        navigate('/live-reserve');
        break;
      case 'daily-transactions':
        navigate('/daily-transactions');
        break;
      case 'real-time-staking':
        navigate('/real-time-staking');
        break;
      case 'startup-funding':
        navigate('/startup-funding');
        break;
      case 'roi-distribution':
        // Already on this page
        break;
      default:
        console.log(`Loading ${itemId} page...`);
    }
  };

  const formatValue = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    }
    return `$${(value / 1000).toFixed(0)}K`;
  };

  const maxValue = Math.max(...chartData.map(d => d.value));
  const minValue = Math.min(...chartData.map(d => d.value));

  return (
    <DashboardLayout className="roi-distribution-container">
      <div className="roi-distribution-content">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">
            <div className="title-with-span">
              Treasury
              <img src={spanImage} alt="Decorative span" className="title-span" />
            </div>
            Dashboard
          </h1>
          <p className="page-subtitle">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            Empowering you to stake with trust.
          </p>
        </div>

        <div className="dashboard-layout">
          {/* Sidebar */}
          <aside className="dashboard-sidebar">
            <nav className="sidebar-nav">
              {sidebarItems.map((item) => (
                <button
                  key={item.id}
                  className={`sidebar-item ${activeTab === item.id ? 'active' : ''}`}
                  onClick={() => handleSidebarClick(item.id)}
                >
                  <img src={item.icon} alt={item.label} className="sidebar-icon" />
                  <span className="sidebar-label">{item.label}</span>
                </button>
              ))}
            </nav>
          </aside>

          {/* Main Dashboard Content */}
          <div className="dashboard-content">
            <div className="roi-header">
              <h2 className="section-title">ROI Distribution</h2>
            </div>

            {/* Stats Cards */}
            <div className="roi-stats">
              <div className="stats-card">
                <div className="stats-label">TOTAL ROI PAID OUT (TO DATE)</div>
                <div className="stats-value">{totalPaidOut}</div>
                <div className="stats-change">
                  <img src={trendUpIcon} alt="Trend up" className="trend-icon" />
                  {changePercent}
                </div>
              </div>

              <div className="stats-card">
                <div className="stats-label">AVERAGE DAILY PAYOUT</div>
                <div className="stats-value">{averageDailyPayout}</div>
              </div>
            </div>

            {/* Chart Section */}
            <div className="chart-section">
              <div className="chart-header">
                <h3 className="chart-title">Monthly ROI Payouts Over Time</h3>
                <div className="chart-filter">
                  <select
                    value={timeFilter}
                    onChange={(e) => setTimeFilter(e.target.value)}
                    className="filter-select"
                  >
                    {timeFilters.map((filter) => (
                      <option key={filter.id} value={filter.id}>
                        {filter.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="chart-container">
                <div className="chart-y-axis">
                  <div className="y-axis-label">{formatValue(maxValue)}</div>
                  <div className="y-axis-label">{formatValue(maxValue * 0.8)}</div>
                  <div className="y-axis-label">{formatValue(maxValue * 0.6)}</div>
                  <div className="y-axis-label">{formatValue(maxValue * 0.4)}</div>
                  <div className="y-axis-label">{formatValue(maxValue * 0.2)}</div>
                  <div className="y-axis-label">$0</div>
                </div>

                <div className="chart-area">
                  <svg className="chart-svg" viewBox="0 0 800 400">
                    {/* Grid lines */}
                    {[0, 1, 2, 3, 4, 5].map((i) => (
                      <line
                        key={i}
                        x1="0"
                        y1={i * 80}
                        x2="800"
                        y2={i * 80}
                        stroke="rgba(255, 255, 255, 0.1)"
                        strokeWidth="1"
                      />
                    ))}

                    {/* Chart line */}
                    <path
                      d={`M ${chartData.map((point, index) => {
                        const x = (index / (chartData.length - 1)) * 800;
                        const y = 400 - ((point.value - minValue) / (maxValue - minValue)) * 400;
                        return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
                      }).join(' ')}`}
                      fill="none"
                      stroke="#4AFF4A"
                      strokeWidth="3"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />

                    {/* Data points */}
                    {chartData.map((point, index) => {
                      const x = (index / (chartData.length - 1)) * 800;
                      const y = 400 - ((point.value - minValue) / (maxValue - minValue)) * 400;
                      return (
                        <circle
                          key={index}
                          cx={x}
                          cy={y}
                          r="4"
                          fill="#4AFF4A"
                          stroke="#262626"
                          strokeWidth="2"
                        />
                      );
                    })}
                  </svg>

                  <div className="chart-x-axis">
                    {chartData.map((point, index) => (
                      <div key={index} className="x-axis-label">
                        {point.month}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ROIDistribution;
