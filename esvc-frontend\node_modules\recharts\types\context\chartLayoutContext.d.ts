import { Cartesian<PERSON>iewBoxRequired, ChartOffsetRequired, LayoutType, <PERSON><PERSON>, Size } from '../util/types';
import { RechartsRootState } from '../state/store';
export declare const useViewBox: () => CartesianViewBoxRequired | undefined;
export declare const useOffset: () => ChartOffsetRequired;
/**
 * Returns the width of the chart in pixels.
 *
 * If you are using chart with hardcoded `width` prop, then the width returned will be the same
 * as the `width` prop on the main chart element.
 *
 * If you are using a chart with a `ResponsiveContainer`, the width will be the size of the chart
 * as the ResponsiveContainer has decided it would be.
 *
 * If the chart has any axes or legend, the `width` will be the size of the chart
 * including the axes and legend. Meaning: adding axes and legend will not change the width.
 *
 * The dimensions do not scale, meaning as user zoom in and out, the width number will not change
 * as the chart gets visually larger or smaller.
 *
 * Returns `undefined` if used outside a chart context.
 *
 * @returns {number | undefined} The width of the chart in pixels, or `undefined` if not in a chart context.
 */
export declare const useChartWidth: () => number | undefined;
/**
 * Returns the height of the chart in pixels.
 *
 * If you are using chart with hardcoded `height` props, then the height returned will be the same
 * as the `height` prop on the main chart element.
 *
 * If you are using a chart with a `ResponsiveContainer`, the height will be the size of the chart
 * as the ResponsiveContainer has decided it would be.
 *
 * If the chart has any axes or legend, the `height` will be the size of the chart
 * including the axes and legend. Meaning: adding axes and legend will not change the height.
 *
 * The dimensions do not scale, meaning as user zoom in and out, the height number will not change
 * as the chart gets visually larger or smaller.
 *
 * Returns `undefined` if used outside a chart context.
 *
 * @returns {number | undefined} The height of the chart in pixels, or `undefined` if not in a chart context.
 */
export declare const useChartHeight: () => number | undefined;
export declare const useMargin: () => Margin;
export declare const selectChartLayout: (state: RechartsRootState) => LayoutType;
export declare const useChartLayout: () => LayoutType;
export declare const ReportChartSize: (props: Size) => null;
export declare const ReportChartMargin: ({ margin }: {
    margin: Margin;
}) => null;
