import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, <PERSON><PERSON>hart, Bar, <PERSON>hart, Pie, Cell } from 'recharts';
import '../styles/components/VisualAnalytics.css';
import DashboardLayout from './DashboardLayout';

// Import icons
import overviewIcon from '../assets/overview.png';
import walletMoneyIcon from '../assets/wallet-money.png';
import arrow2Icon from '../assets/arrow-2.png';
import cardCoinIcon from '../assets/card-coin.png';
import moneysIcon from '../assets/moneys.png';
import percentageSquareIcon from '../assets/percentage-square.png';
import chartIcon from '../assets/chart.png';
import spanImage from '../assets/span.png';

interface VisualAnalyticsProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const VisualAnalytics: React.FC<VisualAnalyticsProps> = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('visual-analytics');
  const [isMobile, setIsMobile] = useState(false);
  const sidebarNavRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Center active tab on mobile
  useEffect(() => {
    const centerActiveTab = () => {
      if (window.innerWidth <= 768 && sidebarNavRef.current) {
        const activeButton = sidebarNavRef.current.querySelector('.sidebar-item.active') as HTMLElement;
        if (activeButton) {
          const container = sidebarNavRef.current;
          const containerWidth = container.offsetWidth;
          const buttonWidth = activeButton.offsetWidth;
          const buttonLeft = activeButton.offsetLeft;

          const scrollLeft = buttonLeft - (containerWidth / 2) + (buttonWidth / 2);
          container.scrollTo({
            left: scrollLeft,
            behavior: 'smooth'
          });
        }
      }
    };

    centerActiveTab();
    window.addEventListener('resize', centerActiveTab);
    return () => window.removeEventListener('resize', centerActiveTab);
  }, [activeTab]);

  const sidebarItems = [
    { id: 'overview', label: 'Overview', icon: overviewIcon },
    { id: 'live-reserve', label: 'Live Reserve', icon: walletMoneyIcon },
    { id: 'daily-transactions', label: 'Daily Transactions', icon: arrow2Icon },
    { id: 'real-time-staking', label: 'Real-Time Staking', icon: cardCoinIcon },
    { id: 'startup-funding', label: 'Startup Funding', icon: moneysIcon },
    { id: 'roi-distribution', label: 'ROI Distribution', icon: percentageSquareIcon },
    { id: 'visual-analytics', label: 'Visual Analytics', icon: chartIcon, active: true },
  ];

  const handleSidebarClick = (itemId: string) => {
    setActiveTab(itemId);
    switch (itemId) {
      case 'overview':
        navigate('/overview');
        break;
      case 'live-reserve':
        navigate('/live-reserve');
        break;
      case 'daily-transactions':
        navigate('/daily-transactions');
        break;
      case 'real-time-staking':
        navigate('/real-time-staking');
        break;
      case 'startup-funding':
        navigate('/startup-funding');
        break;
      case 'roi-distribution':
        navigate('/roi-distribution');
        break;
      case 'visual-analytics':
        // Already on this page
        break;
      default:
        console.log(`Loading ${itemId} page...`);
    }
  };

  // Growth of Total Staked Over Time data
  const stakingGrowthData = [
    { month: 'Jan', value: 200000 },
    { month: 'Feb', value: 250000 },
    { month: 'Mar', value: 320000 },
    { month: 'Apr', value: 380000 },
    { month: 'May', value: 450000 },
    { month: 'Jun', value: 520000 },
    { month: 'Jul', value: 580000 },
    { month: 'Aug', value: 650000 }
  ];

  // ROI Payouts by Tier data
  const roiByTierData = [
    { tier: '$50 - $99', amount: 850000 },
    { tier: '$100 - $249', amount: 1200000 },
    { tier: '$250 - $499', amount: 980000 },
    { tier: '$500 - $999', amount: 1450000 },
    { tier: '$1000 - $2499', amount: 750000 },
    { tier: '$2500 - $4999', amount: 1100000 },
    { tier: '$5000 - $9999', amount: 1350000 },
    { tier: '$10000 - $19999', amount: 920000 },
    { tier: '$20000 - $49999', amount: 1180000 },
    { tier: '$50000+', amount: 1250000 }
  ];

  // Token Reserve Distribution data
  const tokenDistributionData = [
    { name: 'BTC Holdings', value: 91000, color: '#F7931A' },
    { name: 'Solana Holdings', value: 124000, color: '#9945FF' },
    { name: 'USDC Holdings', value: 61800, color: '#2775CA' },
    { name: 'ESVC Holdings', value: 51500, color: '#FF6B35' }
  ];

  const totalValue = tokenDistributionData.reduce((sum, item) => sum + item.value, 0);

  const formatValue = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    }
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}K`;
    }
    return `$${value}`;
  };

  const formatYAxisTick = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    }
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}K`;
    }
    return `$${value}`;
  };

  return (
    <DashboardLayout className="visual-analytics-container">
      <div className="visual-analytics-content">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">
            <div className="title-with-span">
              Visual Analytics
              <img src={spanImage} alt="Decorative span" className="title-span" />
            </div>
          </h1>
        </div>

        <div className="dashboard-layout">
          {/* Sidebar */}
          <aside className="dashboard-sidebar">
            <nav className="sidebar-nav" ref={sidebarNavRef}>
              {sidebarItems.map((item) => (
                <button
                  key={item.id}
                  className={`sidebar-item ${activeTab === item.id ? 'active' : ''}`}
                  onClick={() => handleSidebarClick(item.id)}
                >
                  <img src={item.icon} alt={item.label} className="sidebar-icon" />
                  <span className="sidebar-label">{item.label}</span>
                </button>
              ))}
            </nav>
          </aside>

          {/* Main Dashboard Content */}
          <div className="dashboard-content">
            <div className="visual-analytics-header">
              <h2 className="section-title">Visual Analytics</h2>
            </div>

            {/* Charts Container */}
            <div className="charts-container">
          {/* Growth of Total Staked Over Time */}
          <div className="chart-section">
            <div className="chart-header">
              <h3 className="chart-title">Growth of Total Staked Over Time</h3>
              <div className="chart-filter">
                <select className="filter-select">
                  <option value="this-year">This year</option>
                  <option value="last-year">Last year</option>
                  <option value="all-time">All time</option>
                </select>
              </div>
            </div>
            <div className="chart-container">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={stakingGrowthData}
                  margin={{
                    top: 20,
                    right: isMobile ? 10 : 20,
                    left: isMobile ? 5 : 10,
                    bottom: 20,
                  }}
                >
                  <CartesianGrid 
                    strokeDasharray="none" 
                    stroke="rgba(255, 255, 255, 0.1)" 
                    horizontal={true}
                    vertical={false}
                  />
                  <XAxis 
                    dataKey="month" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ 
                      fill: '#999999', 
                      fontSize: isMobile ? 10 : 12, 
                      fontFamily: 'Montserrat' 
                    }}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ 
                      fill: '#999999', 
                      fontSize: isMobile ? 10 : 12, 
                      fontFamily: 'Montserrat' 
                    }}
                    tickFormatter={formatYAxisTick}
                    domain={['dataMin', 'dataMax']}
                    width={isMobile ? 45 : 60}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="value" 
                    stroke="#4AFF4A" 
                    strokeWidth={isMobile ? 2 : 3}
                    dot={{ 
                      fill: '#4AFF4A', 
                      stroke: '#262626', 
                      strokeWidth: 2, 
                      r: isMobile ? 3 : 4 
                    }}
                    activeDot={{ 
                      r: isMobile ? 5 : 6, 
                      fill: '#4AFF4A', 
                      stroke: '#262626', 
                      strokeWidth: 2 
                    }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* ROI Payouts by Tier */}
          <div className="chart-section">
            <div className="chart-header">
              <h3 className="chart-title">ROI Payouts by Tier</h3>
              <div className="chart-filter">
                <select className="filter-select">
                  <option value="this-year">This year</option>
                  <option value="last-year">Last year</option>
                  <option value="all-time">All time</option>
                </select>
              </div>
            </div>
            <div className="chart-container bar-chart-container">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={roiByTierData}
                  margin={{
                    top: 20,
                    right: isMobile ? 10 : 20,
                    left: isMobile ? 5 : 10,
                    bottom: 60,
                  }}
                >
                  <CartesianGrid 
                    strokeDasharray="none" 
                    stroke="rgba(255, 255, 255, 0.1)" 
                    horizontal={true}
                    vertical={false}
                  />
                  <XAxis 
                    dataKey="tier" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ 
                      fill: '#999999', 
                      fontSize: isMobile ? 8 : 10, 
                      fontFamily: 'Montserrat' 
                    }}
                    angle={-45}
                    textAnchor="end"
                    height={60}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ 
                      fill: '#999999', 
                      fontSize: isMobile ? 10 : 12, 
                      fontFamily: 'Montserrat' 
                    }}
                    tickFormatter={formatYAxisTick}
                    width={isMobile ? 45 : 60}
                  />
                  <Bar 
                    dataKey="amount" 
                    fill="#FF9500"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Token Reserve Distribution */}
          <div className="chart-section pie-chart-section">
            <div className="chart-header">
              <h3 className="chart-title">Token Reserve Distribution</h3>
            </div>
            <div className="pie-chart-container">
              <div className="pie-chart-wrapper">
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={tokenDistributionData}
                      cx="50%"
                      cy="50%"
                      innerRadius={isMobile ? 40 : 60}
                      outerRadius={isMobile ? 80 : 120}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {tokenDistributionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                  </PieChart>
                </ResponsiveContainer>
                <div className="pie-chart-center">
                  <div className="total-value-label">Total Value</div>
                  <div className="total-value">${totalValue.toLocaleString()}</div>
                </div>
              </div>
              <div className="pie-chart-legend">
                {tokenDistributionData.map((item, index) => (
                  <div key={index} className="legend-item">
                    <div className="legend-color" style={{ backgroundColor: item.color }}></div>
                    <div className="legend-details">
                      <div className="legend-name">{item.name}</div>
                      <div className="legend-value">${item.value.toLocaleString()}</div>
                      <div className="legend-percentage">
                        ({((item.value / totalValue) * 100).toFixed(0)}%)
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default VisualAnalytics;
