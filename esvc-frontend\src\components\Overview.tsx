import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/components/Overview.css';
import DashboardLayout from './DashboardLayout';

// Import icons
import overviewIcon from '../assets/overview.png';
import walletMoneyIcon from '../assets/wallet-money.png';
import arrow2Icon from '../assets/arrow-2.png';
import cardCoinIcon from '../assets/card-coin.png';
import moneysIcon from '../assets/moneys.png';
import percentageSquareIcon from '../assets/percentage-square.png';
import chartIcon from '../assets/chart.png';
import spanImage from '../assets/span.png';
import trendUpIcon from '../assets/trend-up.png';
import trendDownIcon from '../assets/trend-down.png';
import informationCircleIcon from '../assets/information-circle.png';

// Import crypto asset icons
import bitcoinSymbol from '../assets/bitcoin_symbol.png.png';
import solanaIcon from '../assets/solana_icon.jpeg.png';
import usdcIcon from '../assets/usdc.png';
import esvcToken from '../assets/esvc-token.png';

interface OverviewProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const Overview: React.FC<OverviewProps> = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  const sidebarNavRef = useRef<HTMLElement>(null);

  // Center active tab on mobile
  useEffect(() => {
    const centerActiveTab = () => {
      if (window.innerWidth <= 768 && sidebarNavRef.current) {
        const activeButton = sidebarNavRef.current.querySelector('.sidebar-item.active') as HTMLElement;
        if (activeButton) {
          const container = sidebarNavRef.current;
          const containerWidth = container.offsetWidth;
          const buttonWidth = activeButton.offsetWidth;
          const buttonLeft = activeButton.offsetLeft;

          const scrollLeft = buttonLeft - (containerWidth / 2) + (buttonWidth / 2);
          container.scrollTo({
            left: scrollLeft,
            behavior: 'smooth'
          });
        }
      }
    };

    centerActiveTab();
    window.addEventListener('resize', centerActiveTab);
    return () => window.removeEventListener('resize', centerActiveTab);
  }, [activeTab]);

  const sidebarItems = [
    { id: 'overview', label: 'Overview', icon: overviewIcon, active: true },
    { id: 'live-reserve', label: 'Live Reserve', icon: walletMoneyIcon },
    { id: 'daily-transactions', label: 'Daily Transactions', icon: arrow2Icon },
    { id: 'real-time-staking', label: 'Real-Time Staking', icon: cardCoinIcon },
    { id: 'startup-funding', label: 'Startup Funding', icon: moneysIcon },
    { id: 'roi-distribution', label: 'ROI Distribution', icon: percentageSquareIcon },
    { id: 'visual-analytics', label: 'Visual Analytics', icon: chartIcon }
  ];

  const dashboardCards = [
    {
      title: 'TOTAL AMOUNT OF ESVC STAKED',
      value: '177,874,389.00',
      unit: 'ESVC',
      change: '+2.4% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    },
    {
      title: 'FUNDS AVAILABLE FOR DAILY ISO PAYOUTS',
      value: '$609,185',
      change: '+7.8% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    },
    {
      title: 'FUNDS AVAILABLE TO FUND STARTUPS',
      value: '$43,700',
      change: '+4.8% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon,
      titleIcon: informationCircleIcon
    },
    {
      title: 'TOTAL NUMBER OF STAKERS',
      value: '11,302',
      change: '+3.8% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    },
    {
      title: 'TOTAL ESVC SOLD TO DATE',
      value: '$29,400,200.00',
      change: '+2.4% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    },
    {
      title: 'TOTAL PROFIT GENERATED',
      value: '$43,700',
      change: '+1.8% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon
    }
  ];

  const handleSidebarClick = (itemId: string) => {
    setActiveTab(itemId);

    // Navigate to different pages based on the tab
    switch (itemId) {
      case 'overview':
        // Already on this page
        break;
      case 'live-reserve':
        navigate('/live-reserve');
        break;
      case 'daily-transactions':
        navigate('/daily-transactions');
        break;
      case 'real-time-staking':
        navigate('/real-time-staking');
        break;
      case 'startup-funding':
        navigate('/startup-funding');
        break;
      case 'roi-distribution':
        navigate('/roi-distribution');
        break;
      case 'visual-analytics':
        navigate('/visual-analytics');
        break;
      default:
        console.log(`Loading ${itemId} page...`);
    }
  };

  return (
    <DashboardLayout className="overview-container">
      <div className="overview-content">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">
            <div className="title-with-span">
              Treasury
              <img src={spanImage} alt="Decorative span" className="title-span" />
            </div>
            Dashboard
          </h1>
          <p className="page-subtitle">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            Empowering you to stake with trust.
          </p>
        </div>

        <div className="dashboard-layout">
          {/* Sidebar */}
          <aside className="dashboard-sidebar">
            <nav className="sidebar-nav" ref={sidebarNavRef}>
              {sidebarItems.map((item) => (
                <button
                  key={item.id}
                  className={`sidebar-item ${activeTab === item.id ? 'active' : ''}`}
                  onClick={() => handleSidebarClick(item.id)}
                >
                  <img src={item.icon} alt={item.label} className="sidebar-icon" />
                  <span className="sidebar-label">{item.label}</span>
                </button>
              ))}
            </nav>
          </aside>

          {/* Main Dashboard Content */}
          <div className="dashboard-content">
            <div className="overview-header">
              <h2 className="section-title">Overview</h2>
            </div>

            <div className="dashboard-cards">
              {dashboardCards.map((card, index) => (
                <div key={index} className="dashboard-card">
                  <div className="card-header">
                    <h3 className="card-title">
                      {card.title}
                      {card.titleIcon && <img src={card.titleIcon} alt="Title icon" className="title-icon" />}
                    </h3>
                  </div>
                  <div className="card-content">
                    <div className="card-value">
                      {card.value}
                      {card.unit && <span className="card-unit">{card.unit}</span>}
                    </div>
                    <div className={`card-change ${card.changeType}`}>
                      <img src={card.changeIcon} alt="Change indicator" className="change-icon" />
                      {card.change}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Overview;
